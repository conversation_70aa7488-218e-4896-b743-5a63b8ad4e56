/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;

import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public class ActivityServiceTestUtil {
  protected static CompletableFuture<ClientActivityResponse> mockClientActivityAsyncResponse() {
    try {
      return CompletableFuture.supplyAsync(
          () -> mockFromFile("full_activity_response.json", ClientActivityResponse.class));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  protected static CompletableFuture<ClientActivityResponse>
      mockClientActivityAsyncResponseWithNullDueDate() {
    try {
      return CompletableFuture.supplyAsync(
          () -> mockFromFile("activity_null_due_date.json", ClientActivityResponse.class));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  protected static CompletableFuture<ClientActivityResponse> mockEmptyActivityAsyncResponse() {
    return futureFromClientActivityResponse(
        mockFromFile("empty_activity_response.json", ClientActivityResponse.class));
  }

  protected static CompletableFuture<ClientActivityResponse> futureFromClientActivityResponse(
      ClientActivityResponse response) {
    try {
      return CompletableFuture.supplyAsync(() -> response);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /** Mock Core Transfers activities - returns TDSCoreTransferActivityResponse with 2 activities */
  protected static CompletableFuture<com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse> mockCoreTransfersActivitiesAsync() {
    com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse response =
        new com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse();

    List<com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity> activities = new ArrayList<>();

    // Add 2 Core Transfer activities
    com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity activity1 =
        new com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity();
    activity1.setReferenceId("ct-1");
    activity1.setFromAccountId("core-from-ct-1");
    activity1.setToAccountId("core-to-ct-1");
    activity1.setAmount(new java.math.BigDecimal("100.00"));
    activity1.setTransferStatus("SUCCESS");
    activity1.setCreatedDate(LocalDate.now().minusDays(1));
    activity1.setExpectedPostingDate(LocalDate.now().minusDays(1));

    com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity activity2 =
        new com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity();
    activity2.setReferenceId("ct-2");
    activity2.setFromAccountId("core-from-ct-2");
    activity2.setToAccountId("core-to-ct-2");
    activity2.setAmount(new java.math.BigDecimal("200.00"));
    activity2.setTransferStatus("VALIDATION_FAILURE");
    activity2.setCreatedDate(LocalDate.now().minusDays(2));
    activity2.setExpectedPostingDate(LocalDate.now().minusDays(2));

    activities.add(activity1);
    activities.add(activity2);
    response.setTransferActivities(activities);

    return CompletableFuture.completedFuture(response);
  }

  /** Mock empty Core Transfers activities */
  protected static CompletableFuture<com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse> mockEmptyCoreTransfersActivitiesAsync() {
    com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse response =
        new com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse();
    response.setTransferActivities(new ArrayList<>());
    return CompletableFuture.completedFuture(response);
  }

  /** Mock Core Transfers activities that throws exception */
  protected static CompletableFuture<com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse> mockFailingCoreTransfersActivitiesAsync() {
    CompletableFuture<com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse> future = new CompletableFuture<>();
    future.completeExceptionally(new RuntimeException("Core Transfers service unavailable"));
    return future;
  }

  private static BffActivity createCoreTransferActivity(
      String id, String status, LocalDate dueDate) {
    BffActivity activity = new BffActivity();
    activity.setId(id);
    activity.setDisplayId(id);
    activity.setDisplayStatus(status);
    activity.setDueDate(dueDate);
    activity.setCreateTimestamp(LocalDateTime.now());
    activity.setFromAccountId("core-from-" + id);
    activity.setToAccountId("core-to-" + id);
    activity.setFromAccountName("Core Transfer From Account");
    activity.setToAccountName("Core Transfer To Account");
    activity.setAmount(100.0);
    activity.setActivityType(ActivityBase.INTERNAL_TRANSFER);
    activity.setMemo("Core Transfer Activity " + id);
    return activity;
  }
}
