/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersActivityMapper;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CoreTransfersActivityService {

  private final CoreTransfersActivityServiceAsync coreTransfersActivityServiceAsync;
  private final CoreTransfersActivityMapper coreTransfersActivityMapper;

  public CoreTransfersActivityService(
      CoreTransfersActivityServiceAsync coreTransfersActivityServiceAsync,
      CoreTransfersActivityMapper coreTransfersActivityMapper) {
    this.coreTransfersActivityServiceAsync = coreTransfersActivityServiceAsync;
    this.coreTransfersActivityMapper = coreTransfersActivityMapper;
  }

  /**
   * Gets Core Transfers activities asynchronously and maps them to BffActivity format with account
   * list
   *
   * @return CompletableFuture containing list of mapped BffActivity objects
   */
  public CompletableFuture<TDSCoreTransferActivityResponse> getCoreTransfersActivitiesAsync() {
    return coreTransfersActivityServiceAsync.getCoreTransferActivityAsync();
  }

  public List<BffActivity> mapCoreTransfersActivities(
      TDSCoreTransferActivityResponse response, List<InternalAccount> accountList) {
    if (response == null || response.getTransferActivities() == null) {
      log.debug("Core Transfers response was null or empty");
      return Collections.emptyList();
    }

    try {
      List<BffActivity> mappedActivities =
          coreTransfersActivityMapper.mapCoreTransferActivities(
              response.getTransferActivities(), accountList);
      log.debug("Successfully mapped {} Core Transfer activities", mappedActivities.size());
      return mappedActivities;
    } catch (Exception e) {
      log.error("Error mapping Core Transfers activities", e);
      return Collections.emptyList();
    }
  }
}
