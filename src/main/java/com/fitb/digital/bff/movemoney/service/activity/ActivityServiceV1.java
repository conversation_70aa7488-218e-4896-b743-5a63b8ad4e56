/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.CORE_TRANSFERS_TDS_ENABLED;
import static com.fitb.digital.bff.movemoney.service.activity.ActivityUtilities.*;
import static com.fitb.digital.bff.movemoney.util.StatusMapper.mapCesToBffResponse;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.RetrievalErrors;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffAddActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetTransferLimitsResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.requests.ClientActivityRequest;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivity;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.bff.movemoney.model.service.activity.ServiceGetActivityResponse;
import com.fitb.digital.bff.movemoney.service.CesClientService;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ActivityServiceV1 extends CesClientService {

  public static final String NO_ACTIVITY_FOUND = "NO_ACTIVITY_FOUND";
  public static final String GENERAL_ERROR = "GENERAL_ERROR";
  public static final String PROCESSED = "Processed";
  public static final String COMPLETED = "Completed";
  public static final String PARTIAL_SUCCESS = "PARTIAL_SUCCESS";

  private final ModelMapper mapper = new ModelMapper();
  private final ModelMapper serviceResponseMapper = new ModelMapper();
  public final ModelMapper activityMapper = new ModelMapper();

  private final ActivityServiceAsync activityServiceAsync;
  private final FeatureFlagService featureFlagService;
  private final CoreTransfersActivityService coreTransfersActivityService;
  private final AccountServiceAsync accountServiceAsync;

  static class UnknownActivityTypeException extends RuntimeException {
    public UnknownActivityTypeException() {
      super();
    }

    public UnknownActivityTypeException(String message) {
      super(message);
    }
  }

  public ActivityServiceV1(
      CesClient cesClient,
      ActivityServiceAsync asyncActivityService,
      FeatureFlagService featureFlagService,
      CoreTransfersActivityService coreTransfersActivityService,
      AccountServiceAsync accountServiceAsync) {
    super(cesClient);
    this.activityServiceAsync = asyncActivityService;
    this.featureFlagService = featureFlagService;
    this.coreTransfersActivityService = coreTransfersActivityService;
    this.accountServiceAsync = accountServiceAsync;
    activityMapper
        .typeMap(ClientActivity.class, BffActivity.class)
        .addMappings(
            m -> m.map(ClientActivity::getNormalizedDisplayStatus, BffActivity::setDisplayStatus));
  }

  // NB: Note: Currently CES requires that a call to /transferandpay/profile is made before an
  // activity can be added
  // We are not accounting for this here and just assumming a call has been made as that is the
  // normal flow through mobile
  // In the future we may need to do a retry or force a call to /transferandpay/profile
  public BffAddActivityResponse addActivity(BffActivityRequest addActivity) {
    ClientActivityRequest clientActivityRequest =
        mapper.map(addActivity, ClientActivityRequest.class);

    var clientResponse =
        super.callClient(() -> cesClient.postTransferAndPayActivity(clientActivityRequest));

    ClientActivity newClientActivity = getFirstActivityInResponse(clientResponse).orElse(null);

    BffActivity bffActivity =
        newClientActivity != null ? mapper.map(newClientActivity, BffActivity.class) : null;
    var bffAddActivityResponse = new BffAddActivityResponse();
    mapCesToBffResponse(clientResponse, bffAddActivityResponse);
    bffAddActivityResponse.setActivity(bffActivity);
    return bffAddActivityResponse;
  }

  public BffAddActivityResponse editActivity(BffActivityRequest editActivity) {
    ClientActivityRequest clientActivityRequest =
        mapper.map(editActivity, ClientActivityRequest.class);

    var response =
        super.callClient(() -> cesClient.editTransferAndPayActivity(clientActivityRequest));
    var bffAddActivityResponse = new BffAddActivityResponse();

    response.getActivities().stream()
        .findFirst()
        .ifPresent(
            activity ->
                bffAddActivityResponse.setActivity(mapper.map(activity, BffActivity.class)));
    mapCesToBffResponse(response, bffAddActivityResponse);
    return bffAddActivityResponse;
  }

  public BffGetTransferLimitsResponse getTransferLimits(String fromAccountId, String toAccountId) {
    var transferLimits =
        super.callClient(() -> cesClient.getTransferLimits(fromAccountId, toAccountId));

    return mapper.map(transferLimits, BffGetTransferLimitsResponse.class);
  }

  public BffResponse cancelActivity(String activityId) {
    super.callGenericClient(() -> cesClient.cancelTransferAndPayActivity(activityId));

    var bffResponse = new BffSimpleResponse();
    bffResponse.setStatus(BffResponse.SUCCESS);

    return bffResponse;
  }

  public BffGetActivityResponse getActivity(Integer recentLimit, Integer upcomingLimit) {
    var bffGetActivityResponse = new BffGetActivityResponse();

    // Start both calls safely to ensure parallel execution even if one fails to start
    CompletableFuture<ClientActivityResponse> cesActivityFuture = null;
    CompletableFuture<TDSCoreTransferActivityResponse> coreTransfersActivityFuture = null;
    CompletableFuture<ListResponse> accountListFuture = null;

    boolean cesCallFailed = false;
    boolean coreTransfersCallFailed = false;
    boolean coreTransfersEnabled = featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED);

    // Start CES call
    try {
      cesActivityFuture = activityServiceAsync.getActivityAsync();
    } catch (Exception ex) {
      log.error("Failed to start CES activity call", ex);
      cesActivityFuture = CompletableFuture.completedFuture(null);
      cesCallFailed = true;
    }

    // Start account service call if core transfers LD flag is enabled
    if (coreTransfersEnabled) {
      try {
        accountListFuture = accountServiceAsync.getAccountListAsync(true);
      } catch (Exception ex) {
        log.error("Failed to start account service call", ex);
        accountListFuture = CompletableFuture.completedFuture(null);
      }
    }

    // Start Core Transfers call if core transfers LD flag is enabled
    if (coreTransfersEnabled) {
      try {
        coreTransfersActivityFuture =
            coreTransfersActivityService.getCoreTransfersActivitiesAsync();
      } catch (Exception ex) {
        log.error("Failed to start Core Transfers activity call", ex);
        coreTransfersActivityFuture = CompletableFuture.completedFuture(null);
        coreTransfersCallFailed = true;
      }
    }

    // Get CES response
    ClientActivityResponse clientActivity = null;
    if (!cesCallFailed) {
      try {
        clientActivity = cesActivityFuture.get();
      } catch (InterruptedException ie) {
        cesCallFailed = true;
        Thread.currentThread().interrupt();
        log.warn("Thread interrupted while retrieving CES activities", ie);
      } catch (Exception ex) {
        cesCallFailed = true;
        log.error("Error retrieving CES activities", ex);
      }
    }

    // Get account list and Core Transfers response if feature flag is enabled
    ListResponse accountListResponse = null;
    List<BffActivity> coreTransfersActivities = null;
    if (coreTransfersEnabled) {
      try {
        accountListResponse = accountListFuture.get();
      } catch (InterruptedException ie) {
        Thread.currentThread().interrupt();
        log.warn("Thread interrupted while retrieving account list", ie);
      } catch (Exception ex) {
        log.error("Error retrieving account list", ex);
      }

      try {
        List<InternalAccount> accounts =
            (accountListResponse != null && accountListResponse.getAccounts() != null)
                ? accountListResponse.getAccounts()
                : new ArrayList<>();
        coreTransfersActivities =
            coreTransfersActivityService.mapCoreTransfersActivities(
                coreTransfersActivityFuture.get(), accounts);
      } catch (InterruptedException ie) {
        coreTransfersCallFailed = true;
        Thread.currentThread().interrupt();
        log.warn("Thread interrupted while retrieving Core Transfers activities", ie);
      } catch (Exception ex) {
        coreTransfersCallFailed = true;
        log.error("Error retrieving Core Transfers activities", ex);
      }
    }

    // Check if both services failed
    if (cesCallFailed && coreTransfersCallFailed) {
      log.error("Both CES and Core Transfers service calls failed");
      throw BffException.serviceUnavailable("Unable to retrieve activities from any service");
    }

    boolean coreTransfersAvailable =
        coreTransfersEnabled
            && coreTransfersActivities != null
            && !coreTransfersActivities.isEmpty();

    // Handle CES failure but Core Transfers success
    if (cesCallFailed && coreTransfersAvailable) {
      return handleCoreTransfersOnly(coreTransfersActivities, recentLimit, upcomingLimit);
    }

    // Process CES response
    if (clientActivity != null) {
      ServiceGetActivityResponse serviceResponse = mapActivityDetails(clientActivity);
      serviceResponseMapper.map(serviceResponse, bffGetActivityResponse);
      moveCompleteActivitiesToHistory(
          serviceResponse.getActivities(), serviceResponse.getRecurringActivities());
      if (serviceResponse.getActivities().isEmpty()) return bffGetActivityResponse;

      // Not Sorting the recent activities if core transfers are enabled as sorting will be done
      // with Core Transfers merge
      bffGetActivityResponse.setRecentActivities(
          coreTransfersAvailable
              ? serviceResponse.getActivities()
              : getRecentList(serviceResponse.getActivities()));
      bffGetActivityResponse.setUpcomingActivities(
          getUpcomingList(serviceResponse.getActivities()));
      bffGetActivityResponse.setRecurringActivities(serviceResponse.getRecurringActivities());
    }

    // Merge Core Transfers activities if available
    if (coreTransfersAvailable) {
      List<BffActivity> recentActivities =
          new ArrayList<>(bffGetActivityResponse.getRecentActivities());
      // All core transfers are immediate transfers and go to recentActivities regardless of status
      recentActivities.addAll(coreTransfersActivities);
      bffGetActivityResponse.setRecentActivities(getRecentList(recentActivities));
    }

    // Handle Core Transfers failure (CES succeeded)
    if (coreTransfersEnabled && coreTransfersCallFailed) {
      bffGetActivityResponse
          .getRetrievalErrors()
          .add(RetrievalErrors.RETRIEVAL_ERROR_CORE_TRANSFERS);
      bffGetActivityResponse.setStatus(PARTIAL_SUCCESS);
    }

    limitActivity(recentLimit, upcomingLimit, bffGetActivityResponse);

    return bffGetActivityResponse;
  }

  /** Handles scenario where CES failed but Core Transfers succeeded */
  private BffGetActivityResponse handleCoreTransfersOnly(
      List<BffActivity> coreTransfersActivities, Integer recentLimit, Integer upcomingLimit) {
    BffGetActivityResponse response = new BffGetActivityResponse();

    // All core transfers are immediate transfers and go to recentActivities regardless of status
    response.setRecentActivities(getRecentList(coreTransfersActivities));
    response.setRecurringActivities(
        new ArrayList<>()); // No recurring activities in Core Transfers yet

    limitActivity(recentLimit, upcomingLimit, response);
    response.getRetrievalErrors().add(RetrievalErrors.UNABLE_TO_GET_ACTIVITY);
    response.setStatus(PARTIAL_SUCCESS);

    return response;
  }

  private Optional<ClientActivity> getFirstActivityInResponse(ClientActivityResponse response) {
    List<ClientActivity> activities = response.getActivities();
    activities.addAll(response.getRecurringActivities());

    return activities.stream().findFirst();
  }

  public void handleCompletionExceptions(Exception e) {
    if (e.getCause() instanceof CesFeignException fe) {
      log.error("Client exception while building account list.", fe);
      throw fe;
    } else {
      log.error("Exception while building account list.", e.getCause());
    }
    throw BffException.serviceUnavailable("Cannot access CES service.");
  }

  private ServiceGetActivityResponse mapActivityDetails(
      ClientActivityResponse getActivityResponse) {
    ServiceGetActivityResponse activityResponse = new ServiceGetActivityResponse();

    activityMapper.map(getActivityResponse, activityResponse);

    activityResponse.setActivities(
        activityResponse.getActivities().stream()
            .filter(activity -> activity.getDueDate() != null)
            .collect(Collectors.toList()));
    activityResponse.setRecurringActivities(
        activityResponse.getRecurringActivities().stream()
            .filter(activity -> activity.getDueDate() != null)
            .collect(Collectors.toList()));
    for (BffActivity activity : activityResponse.getActivities()) {
      // set the new activityType field using deprecated field.
      activity.setActivityType(activity.getType());

      if (activity.getDisplayStatus().equalsIgnoreCase(PROCESSED))
        activity.setDisplayStatus(COMPLETED);
    }
    for (BffActivity activity : activityResponse.getRecurringActivities()) {
      if (activity.getDisplayStatus().equalsIgnoreCase(PROCESSED))
        activity.setDisplayStatus(COMPLETED);
      // External transfer series seems to send a -1 from CES for this value so we are normalizing
      // this to look like the others, which is null.
      if (activity.getNumberOfActivities() != null && activity.getNumberOfActivities() < 0) {
        activity.setNumberOfActivities(null);
      }
      activity.setActivityType(activity.getType());
      activity.setSeriesTemplate(true);
    }

    return activityResponse;
  }

  protected static BffGetActivityResponse limitActivity(
      Integer recentLimit, Integer upcomingLimit, BffGetActivityResponse bffGetActivityResponse) {
    if (recentLimit != null) {
      if (bffGetActivityResponse.getRecentActivities().size() > recentLimit)
        bffGetActivityResponse.setRecentTruncated(true);

      bffGetActivityResponse.setRecentActivities(
          bffGetActivityResponse.getRecentActivities().stream().limit(recentLimit).toList());
    }

    if (upcomingLimit != null) {
      if (bffGetActivityResponse.getUpcomingActivities().size() > upcomingLimit)
        bffGetActivityResponse.setUpcomingTruncated(true);

      bffGetActivityResponse.setUpcomingActivities(
          bffGetActivityResponse.getUpcomingActivities().stream().limit(upcomingLimit).toList());
    }

    return bffGetActivityResponse;
  }

  @NotNull
  private BffGetActivityResponse retrievalError(BffGetActivityResponse bffGetActivityResponse) {
    bffGetActivityResponse.getRetrievalErrors().add(RetrievalErrors.UNABLE_TO_GET_ACTIVITY);
    return bffGetActivityResponse;
  }
}
